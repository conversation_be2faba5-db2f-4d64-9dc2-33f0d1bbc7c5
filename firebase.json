{"functions": [{"source": "apps/api", "codebase": "api", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "firestore": {"database": "(default)", "location": "nam5", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"ui": {"enabled": true}, "auth": {"enabled": true, "port": 9099}, "firestore": {"enabled": true, "port": 8080}, "functions": {"port": 5001}, "singleProjectMode": true}}