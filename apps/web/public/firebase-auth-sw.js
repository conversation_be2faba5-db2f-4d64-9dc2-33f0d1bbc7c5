// Firebase Auth Service Worker
// This service worker intercepts requests and adds Firebase auth tokens to headers

importScripts('https://www.gstatic.com/firebasejs/11.10.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.10.0/firebase-auth-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.10.0/firebase-app-check-compat.js');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAGqdHqCMd9wRjHVSEHA6f1KIuVQtnuVZc",
  authDomain: "charcaterise.firebaseapp.com",
  projectId: "charcaterise",
  storageBucket: "charcaterise.firebasestorage.app",
  messagingSenderId: "581872908064",
  appId: "1:581872908064:web:fdfbadf078bd3285344945",
  measurementId: "G-324WPYYYR8",
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize App Check (optional - uncomment if you're using App Check)
// const appCheck = firebase.appCheck();
// appCheck.activate('your-app-check-site-key', true);

const auth = firebase.auth();

// Cache for tokens to avoid repeated calls
let cachedAuthToken = null;
let cachedAppCheckToken = null;
let tokenCacheTime = 0;
const TOKEN_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get fresh auth token
 */
async function getAuthToken() {
  try {
    const user = auth.currentUser;
    if (!user) return null;
    
    // Refresh token to ensure it's valid
    const token = await user.getIdToken(true);
    return token;
  } catch (error) {
    console.error('Failed to get auth token:', error);
    return null;
  }
}

/**
 * Get fresh app check token
 */
async function getAppCheckToken() {
  try {
    // Uncomment if using App Check
    // const appCheckTokenResponse = await firebase.appCheck().getToken();
    // return appCheckTokenResponse.token;
    return null;
  } catch (error) {
    console.error('Failed to get app check token:', error);
    return null;
  }
}

/**
 * Get cached or fresh tokens
 */
async function getTokens() {
  const now = Date.now();
  
  // Return cached tokens if they're still fresh
  if (cachedAuthToken && (now - tokenCacheTime) < TOKEN_CACHE_DURATION) {
    return {
      authToken: cachedAuthToken,
      appCheckToken: cachedAppCheckToken,
    };
  }
  
  // Get fresh tokens
  const [authToken, appCheckToken] = await Promise.all([
    getAuthToken(),
    getAppCheckToken(),
  ]);
  
  // Cache the tokens
  cachedAuthToken = authToken;
  cachedAppCheckToken = appCheckToken;
  tokenCacheTime = now;
  
  return { authToken, appCheckToken };
}

/**
 * Check if request should have auth tokens attached
 */
function shouldAttachTokens(url) {
  const urlObj = new URL(url);
  
  // Only attach tokens to same-origin requests
  if (urlObj.origin !== self.location.origin) {
    return false;
  }
  
  // Attach tokens to page requests and API routes
  return urlObj.pathname.startsWith('/') && 
         !urlObj.pathname.startsWith('/auth/') &&
         !urlObj.pathname.startsWith('/_next/static/') &&
         !urlObj.pathname.startsWith('/favicon.ico');
}

// Listen for fetch events
self.addEventListener('fetch', (event) => {
  const { request } = event;
  
  // Only process GET requests that should have tokens
  if (request.method !== 'GET' || !shouldAttachTokens(request.url)) {
    return;
  }
  
  event.respondWith(
    (async () => {
      try {
        // Get tokens
        const { authToken, appCheckToken } = await getTokens();
        
        // If no auth token, proceed with original request
        if (!authToken) {
          return fetch(request);
        }
        
        // Clone request and add auth headers
        const headers = new Headers(request.headers);
        headers.set('x-firebase-auth-token', authToken);
        
        if (appCheckToken) {
          headers.set('x-firebase-appcheck-token', appCheckToken);
        }
        
        const modifiedRequest = new Request(request, {
          headers,
        });
        
        return fetch(modifiedRequest);
      } catch (error) {
        console.error('Service worker fetch error:', error);
        // Fallback to original request
        return fetch(request);
      }
    })()
  );
});

// Listen for auth state changes to clear cache
auth.onAuthStateChanged((user) => {
  if (!user) {
    // Clear cached tokens when user signs out
    cachedAuthToken = null;
    cachedAppCheckToken = null;
    tokenCacheTime = 0;
  }
});

// Handle service worker activation
self.addEventListener('activate', (event) => {
  console.log('Firebase Auth Service Worker activated');
  event.waitUntil(self.clients.claim());
});

// Handle service worker installation
self.addEventListener('install', (event) => {
  console.log('Firebase Auth Service Worker installed');
  self.skipWaiting();
});
