"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import type { ProtectedRouteProps } from "@workspace/types/auth";
import { useHybridAuth } from "../../contexts/HybridAuthContext";

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading, serverUser, isHydrated } = useHybridAuth();
  const router = useRouter();

  const isAuthenticated = !!(user || serverUser);

  useEffect(() => {
    // Only redirect if we're sure the user is not authenticated
    // and we've either hydrated or have no server user data
    if (!loading && !isAuthenticated && (isHydrated || !serverUser)) {
      router.push("/auth/signin");
    }
  }, [user, loading, serverUser, isHydrated, isAuthenticated, router]);

  // Show loading only if we're still loading and have no server user data
  if (loading && !serverUser && !isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return <>{children}</>;
};
