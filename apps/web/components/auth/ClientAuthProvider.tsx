'use client';

import { ReactNode } from 'react';
import { HybridAuthProvider } from '../../contexts/HybridAuthContext';

interface ClientAuthProviderProps {
  children: ReactNode;
  initialServerUser?: {
    uid: string;
    email: string | null;
    emailVerified: boolean;
  } | null;
}

/**
 * Client Component that provides hybrid auth context
 */
export function ClientAuthProvider({ children, initialServerUser }: ClientAuthProviderProps) {
  return (
    <HybridAuthProvider initialServerUser={initialServerUser}>
      {children}
    </HybridAuthProvider>
  );
}
