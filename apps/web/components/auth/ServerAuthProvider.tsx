import { ReactNode } from 'react';
import { getServerUser } from '../../lib/server-auth-utils';
import { ClientAuthProvider } from './ClientAuthProvider';

interface ServerAuthProviderProps {
  children: ReactNode;
}

/**
 * Server Component that fetches auth state and passes it to client components
 */
export async function ServerAuthProvider({ children }: ServerAuthProviderProps) {
  // Get server-side user information
  const serverUser = await getServerUser();

  return (
    <ClientAuthProvider initialServerUser={serverUser}>
      {children}
    </ClientAuthProvider>
  );
}
