'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Sparkles,
  Image,
  Video,
  CreditCard,
  Settings,
  HelpCircle,
  Users,
  FileText,
  Zap,
  ChevronDown,
  ChevronRight,
} from 'lucide-react';
import { cn } from '@workspace/ui/lib/utils';
import { Button } from '@workspace/ui/components/button';
import { Separator } from '@workspace/ui/components/separator';
import type { SidebarItem, SidebarSection } from '@workspace/types/ui';

const sidebarSections: SidebarSection[] = [
  {
    title: 'Overview',
    items: [
      { title: 'Discover', href: '/', icon: Home },
      { title: 'AI Characters', href: '/ai-characters', icon: Users },
      { title: 'History', href: '/history', icon: FileText },
      { title: 'My Publications', href: '/publications', icon: Sparkles },
    ],
  },
  {
    title: 'Tools',
    items: [
      { title: 'Face Swap', href: '/faceswap', icon: Users },
      { title: 'AI Image Generator', href: '/ai-generator', icon: Image },
      { title: 'AI Image Editor', href: '/ai-editor', icon: Sparkles },
      { title: 'AI Video Generator', href: '/ai-video', icon: Video },
      { title: 'Image To Image', href: '/image-to-image', icon: Image },
      { title: 'Image to Prompt', href: '/image-to-prompt', icon: FileText },
      { title: 'My New Face', href: '/my-new-face', icon: Users },
      { title: 'Skin Upscaler', href: '/upscaler', icon: Zap },
    ],
  },
  {
    title: 'Payments',
    items: [
      { title: 'Get Gems', href: '/pricing', icon: CreditCard },
      { title: 'Manage subscription', href: '/billing', icon: Settings },
      { title: 'Payment History', href: '/payment-history', icon: FileText },
      { title: 'Pricing', href: '/pricing-info', icon: CreditCard },
    ],
  },
];

const otherItems: SidebarItem[] = [
  { title: 'Support', href: '/support', icon: HelpCircle },
  { title: 'Settings', href: '/settings', icon: Settings },
];

export function Sidebar() {
  const pathname = usePathname();
  const [expandedSections, setExpandedSections] = React.useState<string[]>([
    'Overview',
    'Tools',
    'Payments',
  ]);

  const toggleSection = (sectionTitle: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionTitle)
        ? prev.filter((title) => title !== sectionTitle)
        : [...prev, sectionTitle]
    );
  };

  return (
    <div className="flex h-full w-64 flex-col bg-background border-r border-border">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-border">
        <Link href="/" className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
            <Sparkles className="h-5 w-5 text-white" />
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent">
            Characterise
          </span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-4 space-y-6">
        {sidebarSections.map((section) => (
          <div key={section.title}>
            <Button
              variant="ghost"
              onClick={() => toggleSection(section.title)}
              className="flex w-full items-center justify-between text-sm font-medium text-muted-foreground hover:text-foreground mb-2 h-auto p-0"
            >
              {section.title}
              {expandedSections.includes(section.title) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
            {expandedSections.includes(section.title) && (
              <div className="space-y-1">
                {section.items.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm transition-all duration-200',
                      pathname === item.href
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:translate-x-1'
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                    {item.badge && (
                      <span className="ml-auto rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}

        {/* Other items */}
        <Separator />
        <div className="space-y-1">
          {otherItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm transition-all duration-200',
                pathname === item.href
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:translate-x-1'
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
            </Link>
          ))}
        </div>
      </nav>
    </div>
  );
}
