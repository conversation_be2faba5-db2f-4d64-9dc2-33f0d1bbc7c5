import { NextRequest, NextResponse } from 'next/server';
import { extractAuthToken, verifyServerAuth } from './lib/firebase-server';

// Define protected routes that require authentication
const PROTECTED_ROUTES = [
  '/(protected)',
  '/ai-characters',
  '/ai-generator',
  '/faceswap',
  '/dashboard',
];

// Define public routes that should redirect authenticated users
const AUTH_ROUTES = [
  '/auth/signin',
  '/auth/signup',
  '/auth/forgot-password',
];

// Define routes that don't need auth checking
const PUBLIC_ROUTES = [
  '/',
  '/api',
  '/_next',
  '/favicon.ico',
  '/firebase-auth-sw.js',
];

/**
 * Check if a path matches any of the given patterns
 */
function matchesRoutes(pathname: string, routes: string[]): boolean {
  return routes.some(route => {
    if (route.includes('(') && route.includes(')')) {
      // Handle Next.js route groups like /(protected)
      const pattern = route.replace(/\([^)]*\)/g, '');
      return pathname.startsWith(pattern) || pathname === pattern;
    }
    return pathname.startsWith(route) || pathname === route;
  });
}

/**
 * Check if the request is for a static asset or API route
 */
function isStaticAsset(pathname: string): boolean {
  return (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    pathname === '/firebase-auth-sw.js'
  );
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static assets and API routes
  if (isStaticAsset(pathname)) {
    return NextResponse.next();
  }

  // Skip middleware for public routes
  if (matchesRoutes(pathname, PUBLIC_ROUTES)) {
    return NextResponse.next();
  }

  try {
    // Extract auth token from headers (set by service worker)
    const authToken = extractAuthToken(request.headers);
    
    // Verify authentication
    const authResult = await verifyServerAuth(authToken);
    const isAuthenticated = authResult.isAuthenticated;

    // Handle protected routes
    if (matchesRoutes(pathname, PROTECTED_ROUTES)) {
      if (!isAuthenticated) {
        // Redirect to sign-in page with return URL
        const signInUrl = new URL('/auth/signin', request.url);
        signInUrl.searchParams.set('returnUrl', pathname);
        return NextResponse.redirect(signInUrl);
      }
      
      // User is authenticated, allow access
      const response = NextResponse.next();
      
      // Add user info to headers for server components
      if (authResult.user) {
        response.headers.set('x-user-id', authResult.user.uid);
        response.headers.set('x-user-email', authResult.user.email || '');
        response.headers.set('x-user-verified', authResult.user.emailVerified.toString());
      }
      
      return response;
    }

    // Handle auth routes (signin, signup, etc.)
    if (matchesRoutes(pathname, AUTH_ROUTES)) {
      if (isAuthenticated) {
        // User is already authenticated, redirect to dashboard
        const returnUrl = request.nextUrl.searchParams.get('returnUrl');
        const redirectUrl = returnUrl && returnUrl.startsWith('/') 
          ? new URL(returnUrl, request.url)
          : new URL('/', request.url);
        return NextResponse.redirect(redirectUrl);
      }
      
      // User is not authenticated, allow access to auth pages
      return NextResponse.next();
    }

    // For all other routes, just pass through
    return NextResponse.next();

  } catch (error) {
    console.error('Middleware error:', error);
    
    // On error, allow the request to proceed
    // This ensures the app doesn't break if auth verification fails
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
