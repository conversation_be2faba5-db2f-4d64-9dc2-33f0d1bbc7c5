# Firebase Auth SSR Implementation

This document explains the Firebase Authentication Server-Side Rendering (SSR) implementation in the Characterise web app.

## Overview

The SSR auth implementation provides seamless authentication across server and client environments, eliminating auth flicker and enabling true server-side protection of routes.

## Architecture

### Components

1. **Service Worker** (`/public/firebase-auth-sw.js`)
   - Intercepts requests and adds Firebase auth tokens to headers
   - Manages token caching and refresh
   - Ensures tokens are available for SSR

2. **Middleware** (`/middleware.ts`)
   - Verifies auth tokens on the server
   - Handles route protection and redirects
   - Adds user info to request headers

3. **Server Auth Utils** (`/lib/auth-server.ts`, `/lib/firebase-server.ts`)
   - Server-side Firebase configuration
   - Auth token verification
   - User data extraction

4. **Hybrid Auth Context** (`/contexts/HybridAuthContext.tsx`)
   - Combines server and client auth states
   - Prevents auth flicker
   - Manages auth state transitions

## How It Works

### 1. Initial Request
```
User Request → Service Worker → Middleware → Server Component
     ↓              ↓              ↓              ↓
   Browser    Add Auth Token   Verify Token   Get User Data
```

### 2. Server-Side Rendering
- Middleware verifies auth tokens and adds user info to headers
- Server components can access user data immediately
- Protected routes are truly protected on the server

### 3. Client Hydration
- Client auth context initializes with server user data
- Firebase Auth state loads in the background
- Seamless transition from server to client state

## Key Files

### Server-Side
- `lib/firebase-server.ts` - Server Firebase configuration
- `lib/auth-server.ts` - Server auth utilities
- `lib/server-auth-utils.ts` - Helper functions for server components
- `middleware.ts` - Route protection and auth verification

### Client-Side
- `contexts/HybridAuthContext.tsx` - Hybrid auth context
- `lib/service-worker.ts` - Service worker management
- `public/firebase-auth-sw.js` - Service worker implementation

### Components
- `components/auth/ServerAuthProvider.tsx` - Server auth provider
- `components/auth/ClientAuthProvider.tsx` - Client auth provider
- `components/auth/AuthRedirect.tsx` - Updated for hybrid auth
- `components/auth/ProtectedRoute.tsx` - Updated for hybrid auth

## Usage Examples

### Server Component with Auth
```tsx
import { getServerUser, requireAuth } from '@/lib/server-auth-utils';

export default async function ProtectedPage() {
  // Option 1: Require auth (redirects if not authenticated)
  const user = await requireAuth();
  
  // Option 2: Get user data (returns null if not authenticated)
  const user = await getServerUser();
  
  return <div>Welcome, {user.email}!</div>;
}
```

### Client Component with Hybrid Auth
```tsx
'use client';
import { useHybridAuth } from '@/contexts/HybridAuthContext';

export default function MyComponent() {
  const { user, serverUser, loading, isHydrated } = useHybridAuth();
  
  // Use server user data immediately, client user data when available
  const currentUser = user || serverUser;
  
  return <div>{currentUser?.email}</div>;
}
```

### API Route with Auth
```tsx
import { extractAuthToken, verifyServerAuth } from '@/lib/firebase-server';

export async function GET(request: NextRequest) {
  const authToken = extractAuthToken(request.headers);
  const authResult = await verifyServerAuth(authToken);
  
  if (!authResult.isAuthenticated) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  return NextResponse.json({ user: authResult.user });
}
```

## Benefits

1. **No Auth Flicker** - Server user data prevents loading states
2. **True Server Protection** - Routes are protected before client JS loads
3. **Better SEO** - Authenticated content can be server-rendered
4. **Improved Performance** - Faster initial page loads
5. **Enhanced Security** - Server-side token verification

## Testing

Visit `/auth-test` (when authenticated) to see the SSR auth implementation in action. This page shows both server-side and client-side auth states.

## Troubleshooting

### Service Worker Issues
- Check browser dev tools → Application → Service Workers
- Ensure service worker is registered and active
- Clear service worker cache if needed

### Token Issues
- Check network requests for `x-firebase-auth-token` header
- Verify Firebase configuration matches client and server
- Check middleware logs for token verification errors

### Hydration Issues
- Ensure server and client user data match
- Check for console errors during hydration
- Verify auth context is properly initialized

## Migration from Client-Only Auth

1. Replace `AuthProvider` with `ServerAuthProvider` in root layout
2. Update components to use `useHybridAuth` instead of `useAuth`
3. Convert protected layouts to use `requireAuth()`
4. Update auth redirects to use hybrid auth state
5. Test all auth flows thoroughly

## Security Considerations

- Auth tokens are transmitted via headers (not cookies by default)
- Service worker only attaches tokens to same-origin requests
- Middleware verifies all tokens server-side
- Protected routes are secured at the server level
- Tokens are cached briefly to improve performance
