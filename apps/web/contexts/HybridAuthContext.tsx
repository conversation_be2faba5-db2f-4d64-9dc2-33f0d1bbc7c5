'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { 
  User,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  onAuthStateChanged,
  AuthError
} from 'firebase/auth';
import { auth } from '../lib/firebase';
import { initializeServiceWorker, refreshServiceWorkerTokens } from '../lib/service-worker';

interface HybridAuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  // Server-side user info (from middleware)
  serverUser: {
    uid: string;
    email: string | null;
    emailVerified: boolean;
  } | null;
  isHydrated: boolean;
}

const HybridAuthContext = createContext<HybridAuthContextType | undefined>(undefined);

interface HybridAuthProviderProps {
  children: ReactNode;
  // Server-side user data passed from server components
  initialServerUser?: {
    uid: string;
    email: string | null;
    emailVerified: boolean;
  } | null;
}

export const HybridAuthProvider: React.FC<HybridAuthProviderProps> = ({ 
  children, 
  initialServerUser = null 
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [serverUser, setServerUser] = useState(initialServerUser);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Initialize service worker for SSR token transmission
    initializeServiceWorker().catch(console.error);

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      setLoading(false);
      setIsHydrated(true);
      
      // Update server user state to match client state
      if (user) {
        setServerUser({
          uid: user.uid,
          email: user.email,
          emailVerified: user.emailVerified,
        });
      } else {
        setServerUser(null);
      }
      
      // Refresh service worker tokens when auth state changes
      try {
        await refreshServiceWorkerTokens();
      } catch (error) {
        console.error('Failed to refresh service worker tokens:', error);
      }
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      const authError = error as AuthError;
      throw new Error(getAuthErrorMessage(authError.code));
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await signOut(auth);
      // Clear server user state
      setServerUser(null);
    } catch (error) {
      const authError = error as AuthError;
      throw new Error(getAuthErrorMessage(authError.code));
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      const authError = error as AuthError;
      throw new Error(getAuthErrorMessage(authError.code));
    }
  };

  const value: HybridAuthContextType = {
    user,
    loading,
    signIn,
    logout,
    resetPassword,
    serverUser,
    isHydrated,
  };

  return (
    <HybridAuthContext.Provider value={value}>
      {children}
    </HybridAuthContext.Provider>
  );
};

export const useHybridAuth = (): HybridAuthContextType => {
  const context = useContext(HybridAuthContext);
  if (context === undefined) {
    throw new Error('useHybridAuth must be used within a HybridAuthProvider');
  }
  return context;
};

// Helper function to convert Firebase auth error codes to user-friendly messages
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/user-disabled':
      return 'This account has been disabled.';
    case 'auth/too-many-requests':
      return 'Too many failed login attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    case 'auth/invalid-credential':
      return 'Invalid email or password.';
    default:
      return 'An error occurred during authentication.';
  }
}

// Backward compatibility hook
export const useAuth = useHybridAuth;
