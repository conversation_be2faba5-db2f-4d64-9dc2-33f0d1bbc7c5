import { NextRequest, NextResponse } from 'next/server';
import { extractAuthToken, verifyServerAuth } from '@/lib/firebase-server';

export async function GET(request: NextRequest) {
  try {
    // Extract auth token from headers
    const authToken = extractAuthToken(request.headers);
    
    if (!authToken) {
      return NextResponse.json(
        { error: 'No auth token provided', authenticated: false },
        { status: 401 }
      );
    }

    // Verify the auth token
    const authResult = await verifyServerAuth(authToken);

    if (!authResult.isAuthenticated) {
      return NextResponse.json(
        { 
          error: 'Invalid auth token', 
          authenticated: false,
          details: authResult.error 
        },
        { status: 401 }
      );
    }

    // Return success response with user info
    return NextResponse.json({
      authenticated: true,
      user: authResult.user,
      message: 'Successfully authenticated via API',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('API auth test error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        authenticated: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
