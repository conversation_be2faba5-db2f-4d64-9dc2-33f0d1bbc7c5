'use client';

import React from 'react';
import { Card, CardContent } from '@workspace/ui/components/card';
import { Button } from '@workspace/ui/components/button';
import {
  Plus,
  Users,
  ImageIcon,
  Sparkles,
  ArrowRight,
  CheckCircle,
  Zap,
  Palette,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function AICharactersPage() {
  const router = useRouter();

  const tasks = [
    {
      id: 1,
      title: 'Unlock AI Face Swap',
      description:
        'Add a target face to your AI character to use the face swap tool.',
      icon: Users,
      completed: false,
      action: () => router.push('/ai-characters/create'),
    },
    {
      id: 2,
      title: 'Unlock AI Image generator',
      description: 'Train your AI character to generate consistent AI images.',
      icon: ImageIcon,
      completed: false,
      action: () => router.push('/ai-characters/create'),
    },
  ];

  const tools = [
    {
      id: 1,
      title: 'AI Face Swap',
      description: 'Swap faces in photos with your AI character',
      icon: Users,
      available: false,
      action: () => router.push('/faceswap'),
    },
    {
      id: 2,
      title: 'AI Image Generator',
      description: 'Generate images featuring your AI character',
      icon: Sparkles,
      available: false,
      action: () => router.push('/ai-generator'),
      highlight: true,
    },
  ];

  const characters = [];

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">AI Characters</h1>
          <p className="text-muted-foreground mt-1">
            Create and manage your AI characters for consistent image generation
          </p>
        </div>
      </div>

      {/* Create AI Character CTA */}
      <Card className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-200 dark:border-purple-800">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h2 className="text-xl font-semibold">Create AI Character</h2>
              <p className="text-muted-foreground">
                Start by creating your first AI character to unlock powerful
                tools
              </p>
            </div>
            <Button
              size="lg"
              onClick={() => router.push('/ai-characters/create')}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              <Plus className="mr-2 h-5 w-5" />
              Create Character
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tasks Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Tasks</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tasks.map((task) => (
            <Card
              key={task.id}
              className="cursor-pointer hover:shadow-lg transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div
                    className={`p-3 rounded-lg ${
                      task.completed
                        ? 'bg-green-100 dark:bg-green-900'
                        : 'bg-blue-100 dark:bg-blue-900'
                    }`}
                  >
                    {task.completed ? (
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    ) : (
                      <task.icon className="h-6 w-6 text-blue-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold mb-2">{task.title}</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {task.description}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={task.action}
                      disabled={task.completed}
                    >
                      {task.completed ? 'Completed' : 'Start Task'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* AI Tools Section */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">AI Tools</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tools.map((tool) => (
            <Card
              key={tool.id}
              className={`cursor-pointer transition-all ${
                tool.available
                  ? 'hover:shadow-lg'
                  : 'opacity-60 cursor-not-allowed'
              } ${tool.highlight ? 'ring-2 ring-purple-500/20' : ''}`}
            >
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div
                    className={`p-3 rounded-lg ${
                      tool.available
                        ? 'bg-purple-100 dark:bg-purple-900'
                        : 'bg-gray-100 dark:bg-gray-800'
                    }`}
                  >
                    <tool.icon
                      className={`h-6 w-6 ${
                        tool.available ? 'text-purple-600' : 'text-gray-400'
                      }`}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold">{tool.title}</h3>
                      {!tool.available && (
                        <span className="text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded">
                          Locked
                        </span>
                      )}
                      {tool.highlight && (
                        <Button
                          size="sm"
                          variant="secondary"
                          className="bg-purple-100 text-purple-700 hover:bg-purple-200"
                        >
                          Train
                        </Button>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      {tool.description}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={tool.action}
                      disabled={!tool.available}
                    >
                      {tool.available ? 'Use Tool' : 'Create Character First'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Characters List (Empty State) */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Your Characters</h2>
        {characters.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="font-semibold mb-2">No AI Characters Yet</h3>
              <p className="text-muted-foreground mb-6">
                Create your first AI character to start generating consistent
                images and using face swap tools.
              </p>
              <Button
                onClick={() => router.push('/ai-characters/create')}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Character
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Character cards would go here */}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <Zap className="h-12 w-12 mx-auto text-yellow-500 mb-4" />
            <h3 className="font-semibold mb-2">Fast Setup</h3>
            <p className="text-sm text-muted-foreground">
              Create a character in minutes with just one photo for face
              swapping
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Sparkles className="h-12 w-12 mx-auto text-purple-500 mb-4" />
            <h3 className="font-semibold mb-2">Consistent Results</h3>
            <p className="text-sm text-muted-foreground">
              Train with multiple images for consistent AI-generated content
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Palette className="h-12 w-12 mx-auto text-green-500 mb-4" />
            <h3 className="font-semibold mb-2">Multiple Styles</h3>
            <p className="text-sm text-muted-foreground">
              Use your character across different art styles and scenarios
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
