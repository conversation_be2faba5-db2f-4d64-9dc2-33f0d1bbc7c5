"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { generateImage, getUserMedia, pollJobStatus } from "@/lib/functions";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import {
  Image as ImageIcon,
  Sparkles,
  Download,
  ChevronDown,
  ExternalLink,
  Clock,
  CheckCircle,
  X,
  Heart,
  Share,
  MoreHorizontal,
} from "lucide-react";

interface GeneratedImage {
  id: string;
  requestId: string;
  type: string;
  mediaType: string;
  firebaseUrl: string;
  originalUrl: string;
  createdAt: any;
}

export default function AIGeneratorPage() {
  const { user } = useAuth();
  const [selectedTab, setSelectedTab] = useState<"generate" | "history">(
    "generate"
  );
  const [formData, setFormData] = useState({
    model: "fal-ai/flux/schnell",
    character: "Choose AI Character",
    style: "Ultra Realistic or Selfie",
    promptAdherence: 50,
    seed: "",
    aspectRatio: "9:16",
    numImages: 1,
    prompt: "",
  });

  // Firebase integration state
  const [userMedia, setUserMedia] = useState<GeneratedImage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStatus, setGenerationStatus] = useState<string>("");
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);

  // Load user's generation history
  useEffect(() => {
    if (user && selectedTab === "history") {
      loadUserMedia();
    }
  }, [user, selectedTab]);

  const loadUserMedia = async () => {
    setIsLoadingHistory(true);
    try {
      const result = await getUserMedia({ limit: 20 });
      setUserMedia(result.data.media);
    } catch (error) {
      console.error("Failed to load user media:", error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const handleGenerate = async () => {
    if (!formData.prompt.trim()) return;

    setIsGenerating(true);
    setGenerationStatus("Starting generation...");
    setGeneratedImage(null);

    try {
      // Start image generation
      const result = await generateImage({
        prompt: formData.prompt.trim(),
        model: "fal-ai/fast-sdxl",
        num_inference_steps: 12,
        guidance_scale: 3.5,
        enable_safety_checker: false,
      });

      setGenerationStatus("Generation in progress...");

      // Poll for completion
      const finalStatus = await pollJobStatus(
        result.data.requestId,
        (status) => {
          setGenerationStatus(`Status: ${status.status}`);
        }
      );

      if (finalStatus.status === "COMPLETED" && finalStatus.firebaseUrl) {
        setGeneratedImage(finalStatus.firebaseUrl);
        setGenerationStatus("Generation completed!");
        // Refresh history if on history tab
        if (selectedTab === "history") {
          loadUserMedia();
        }
      } else if (finalStatus.status === "FAILED") {
        setGenerationStatus(
          `Generation failed: ${finalStatus.error || "Unknown error"}`
        );
      }
    } catch (error: any) {
      console.error("Generation error:", error);
      setGenerationStatus(`Error: ${error.message || "Generation failed"}`);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Top Navigation */}
      <div className="border-b border-border bg-card">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Model Name</span>
              <Button variant="ghost" size="sm" className="h-8">
                {formData.model}
                <ChevronDown className="ml-1 h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                AI Influencer
              </span>
              <Button variant="ghost" size="sm" className="h-8">
                {formData.character}
                <ChevronDown className="ml-1 h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">LoRas</span>
              <Button variant="ghost" size="sm" className="h-8">
                {formData.style}
                <ChevronDown className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </div>
          <Button variant="outline" size="sm">
            <ExternalLink className="mr-2 h-4 w-4" />
            Learn How To Use
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex">
        {/* Center Content */}
        <div className="flex-1 p-6">
          <div className="max-w-4xl mx-auto">
            {/* Tab Navigation */}
            <div className="flex space-x-1 mb-8">
              <Button
                variant={selectedTab === "generate" ? "default" : "ghost"}
                onClick={() => setSelectedTab("generate")}
                className="px-6"
              >
                Generate
              </Button>
              <Button
                variant={selectedTab === "history" ? "default" : "ghost"}
                onClick={() => setSelectedTab("history")}
                className="px-6"
              >
                History
              </Button>
            </div>

            {selectedTab === "generate" ? (
              <div className="space-y-8">
                {/* Main Title */}
                <div className="text-center space-y-4">
                  <h1 className="text-4xl font-bold">AI Image Generator</h1>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Create stunning images with AI. Describe what you want to
                    see and our AI will generate it for you.
                  </p>
                </div>

                {/* Generation Status & Result */}
                {isGenerating && (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground mb-4">
                      {generationStatus}
                    </div>
                    <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
                  </div>
                )}

                {generatedImage && (
                  <div className="text-center py-8">
                    <div className="max-w-md mx-auto">
                      <img
                        src={generatedImage}
                        alt="Generated image"
                        className="w-full rounded-lg shadow-lg"
                      />
                      <div className="mt-4 text-sm text-muted-foreground">
                        {generationStatus}
                      </div>
                    </div>
                  </div>
                )}

                {!isGenerating && !generatedImage && (
                  <div className="text-center py-12">
                    <div className="text-muted-foreground mb-4">
                      You currently have no generated elements. Start generating
                      something using the prompt !
                    </div>
                  </div>
                )}

                {/* Prompt Input */}
                <div className="space-y-4">
                  <textarea
                    placeholder="Describe the image you want to generate..."
                    className="w-full px-4 py-3 border border-input bg-background rounded-lg text-sm min-h-[120px] resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                    value={formData.prompt}
                    onChange={(e) =>
                      setFormData({ ...formData, prompt: e.target.value })
                    }
                  />

                  <div className="flex justify-center">
                    <Button
                      size="lg"
                      className="px-8 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                      onClick={handleGenerate}
                      disabled={isGenerating || !formData.prompt.trim()}
                    >
                      {isGenerating ? "Generating..." : "Generate"}
                      <span className="ml-2 px-2 py-1 bg-white/20 rounded text-sm">
                        2 💎
                      </span>
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* History Header */}
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Generation History</h2>
                  <div className="text-sm text-muted-foreground">
                    {userMedia.length} generations
                  </div>
                </div>

                {/* Loading State */}
                {isLoadingHistory ? (
                  <div className="text-center py-12">
                    <div className="text-muted-foreground">
                      Loading your images...
                    </div>
                  </div>
                ) : userMedia.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-muted-foreground">
                      No generated images yet. Start creating some amazing AI
                      art!
                    </div>
                  </div>
                ) : (
                  /* History Grid */
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {userMedia.map((image) => (
                      <Card key={image.id} className="group overflow-hidden">
                        <div className="relative aspect-square bg-muted">
                          <img
                            src={image.firebaseUrl}
                            alt="Generated image"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // Fallback to gradient if image fails to load
                              e.currentTarget.style.display = "none";
                            }}
                          />
                          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-8 w-8 bg-black/50 hover:bg-black/70"
                            >
                              <Heart className="h-4 w-4 text-white" />
                            </Button>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-8 w-8 bg-black/50 hover:bg-black/70"
                              onClick={() =>
                                window.open(image.firebaseUrl, "_blank")
                              }
                            >
                              <Download className="h-4 w-4 text-white" />
                            </Button>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-8 w-8 bg-black/50 hover:bg-black/70"
                            >
                              <MoreHorizontal className="h-4 w-4 text-white" />
                            </Button>
                          </div>
                        </div>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{image.type}</span>
                            <span>
                              {new Date(
                                image.createdAt?.seconds * 1000
                              ).toLocaleDateString()}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        {/* Right Sidebar Controls */}
        <div className="w-80 border-l border-border bg-card p-6 space-y-6">
          {/* Select Model */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Select Model</label>
            <select
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
              value={formData.model}
              onChange={(e) =>
                setFormData({ ...formData, model: e.target.value })
              }
            >
              <option>Flux</option>
              <option>SDXL</option>
              <option>Midjourney</option>
            </select>
          </div>

          {/* AI Character */}
          <div className="space-y-3">
            <label className="text-sm font-medium">AI Character</label>
            <select
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
              value={formData.character}
              onChange={(e) =>
                setFormData({ ...formData, character: e.target.value })
              }
            >
              <option>Choose AI Character</option>
              <option>Test Character</option>
              <option>Emma Model</option>
            </select>
            <Button variant="outline" size="sm" className="w-full">
              Create AI Character
            </Button>
          </div>

          {/* LoRa Style */}
          <div className="space-y-3">
            <label className="text-sm font-medium">LoRa Style</label>
            <select
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
              value={formData.style}
              onChange={(e) =>
                setFormData({ ...formData, style: e.target.value })
              }
            >
              <option>Ultra Realistic or Selfie</option>
              <option>Photorealistic</option>
              <option>Artistic</option>
              <option>Cinematic</option>
              <option>Portrait</option>
            </select>
          </div>

          {/* Prompt Adherence */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Prompt Adherence</label>
            <div className="space-y-2">
              <input
                type="range"
                min="0"
                max="100"
                value={formData.promptAdherence}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    promptAdherence: parseInt(e.target.value),
                  })
                }
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Be Creative</span>
                <span>Follow Prompt</span>
              </div>
            </div>
          </div>

          {/* Seed */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Seed</label>
            <input
              type="text"
              placeholder="Leave empty for random"
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
              value={formData.seed}
              onChange={(e) =>
                setFormData({ ...formData, seed: e.target.value })
              }
            />
          </div>

          {/* Aspect Ratio */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Aspect Ratio</label>
            <div className="grid grid-cols-3 gap-2">
              {["9:16", "1:1", "16:9"].map((ratio) => (
                <button
                  key={ratio}
                  className={`aspect-square border-2 rounded-lg flex items-center justify-center text-sm font-medium transition-colors ${
                    formData.aspectRatio === ratio
                      ? "border-primary bg-primary text-primary-foreground"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() =>
                    setFormData({ ...formData, aspectRatio: ratio })
                  }
                >
                  {ratio}
                </button>
              ))}
            </div>
          </div>

          {/* Number of Images */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Number of Images</label>
            <div className="grid grid-cols-4 gap-2">
              {[1, 2, 3, 4].map((num) => (
                <button
                  key={num}
                  className={`aspect-square border-2 rounded-lg flex items-center justify-center text-sm font-medium transition-colors ${
                    formData.numImages === num
                      ? "border-primary bg-primary text-primary-foreground"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() => setFormData({ ...formData, numImages: num })}
                >
                  {num}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
