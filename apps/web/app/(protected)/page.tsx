import React from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { getServerUser } from "@/lib/server-auth-utils";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";

import {
  Users,
  Image,
  Video,
  Sparkles,
  Zap,
  FileText,
  ArrowRight,
  Star,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
} from "lucide-react";

const toolCards = [
  {
    title: "AI Face Swap",
    description: "The most advanced photo and video face swap",
    icon: Users,
    href: "/faceswap",
    badge: "Popular",
    color: "from-purple-500 to-pink-500",
  },
  {
    title: "AI Image Generator",
    description: "Generate consistent photorealistic AI influencer images",
    icon: Image,
    href: "/ai-generator",
    badge: "New",
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "AI Video Generator",
    description: "Create stunning AI videos in seconds",
    icon: Video,
    href: "/ai-video",
    badge: "Hot",
    color: "from-green-500 to-emerald-500",
  },
  {
    title: "AI Image Editor",
    description: "Edit and enhance your images with AI",
    icon: <PERSON><PERSON><PERSON>,
    href: "/ai-editor",
    color: "from-orange-500 to-red-500",
  },
  {
    title: "Skin Upscaler",
    description: "Upscale your images with realistic details",
    icon: Zap,
    href: "/upscaler",
    color: "from-violet-500 to-purple-500",
  },
  {
    title: "Image to Prompt",
    description: "Generate prompts from your images",
    icon: FileText,
    href: "/image-to-prompt",
    color: "from-teal-500 to-blue-500",
  },
];

const stats = [
  { label: "Total Swaps", value: "12,345", icon: Users },
  { label: "Images Generated", value: "8,901", icon: Image },
  { label: "Active Users", value: "2,345", icon: TrendingUp },
];

const recentActivity = [
  {
    id: 1,
    title: "Face swap completed",
    description: "AI_Influencer_Video_001.mp4",
    time: "2 minutes ago",
    status: "completed",
    icon: CheckCircle,
  },
  {
    id: 2,
    title: "Image generation in progress",
    description: "Generating 4 variations...",
    time: "5 minutes ago",
    status: "processing",
    icon: Clock,
  },
  {
    id: 3,
    title: "Video processing failed",
    description: "beach_video.mp4 - File too large",
    time: "10 minutes ago",
    status: "failed",
    icon: AlertCircle,
  },
  {
    id: 4,
    title: "AI Character created",
    description: "Emma_Model_v2 training completed",
    time: "1 hour ago",
    status: "completed",
    icon: CheckCircle,
  },
];

export default async function Dashboard() {
  // Get server-side user data
  const user = await getServerUser();

  return (
    <div className="p-6 space-y-8">
      {/* Welcome Section */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">
          Welcome to Characterise
          {user?.email ? `, ${user.email.split("@")[0]}` : ""}
        </h1>
        <p className="text-muted-foreground text-lg">
          All the AI tools you need to build your AI influencer empire.
        </p>
        {user && (
          <p className="text-sm text-muted-foreground">
            Account: {user.email} {user.emailVerified ? "✓" : "(unverified)"}
          </p>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => (
          <Card key={stat.label}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.label}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Featured Tools */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold">AI Tools</h2>
          <Button variant="outline">
            View All Tools
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {toolCards.map((tool) => (
            <Card
              key={tool.title}
              className="group hover:shadow-lg transition-all duration-200 cursor-pointer"
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div
                    className={`p-3 rounded-lg bg-gradient-to-r ${tool.color} text-white`}
                  >
                    <tool.icon className="h-6 w-6" />
                  </div>
                  {tool.badge && (
                    <span className="px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                      {tool.badge}
                    </span>
                  )}
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">
                  {tool.title}
                </CardTitle>
                <CardDescription>{tool.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  Launch Tool
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="mr-2 h-5 w-5 text-yellow-500" />
              Quick Start
            </CardTitle>
            <CardDescription>
              Get started with our most popular features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Button
                className="w-full h-auto p-4 justify-start"
                variant="outline"
              >
                <Play className="mr-3 h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">Create AI Character</div>
                  <div className="text-sm text-muted-foreground">
                    Start from AI Character
                  </div>
                </div>
              </Button>
              <Button
                className="w-full h-auto p-4 justify-start"
                variant="outline"
              >
                <Users className="mr-3 h-5 w-5" />
                <div className="text-left">
                  <div className="font-medium">Face Swap Video</div>
                  <div className="text-sm text-muted-foreground">
                    Swap faces in videos
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-blue-500" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Your latest AI generations and processes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div
                    className={`p-2 rounded-full ${
                      activity.status === "completed"
                        ? "bg-green-100 text-green-600"
                        : activity.status === "processing"
                          ? "bg-blue-100 text-blue-600"
                          : "bg-red-100 text-red-600"
                    }`}
                  >
                    <activity.icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground">
                      {activity.title}
                    </p>
                    <p className="text-sm text-muted-foreground truncate">
                      {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
