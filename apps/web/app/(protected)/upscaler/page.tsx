import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@workspace/ui/components/card';
import { Button } from '@workspace/ui/components/button';
import {
  Zap,
  Upload,
  Download,
  ArrowRight,
  Image as ImageIcon,
  Sparkles,
  TrendingUp,
  Clock,
  CheckCircle,
  Settings,
} from 'lucide-react';

export default function UpscalerPage() {
  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Zap className="h-8 w-8 text-green-500" />
          <h1 className="text-3xl font-bold">Skin Upscaler</h1>
        </div>
        <p className="text-muted-foreground text-lg">
          Enhance your images with our unique high quality, ultra-realistic
          upscaler tool.
        </p>
      </div>

      {/* Main Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upload Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Upload Image</CardTitle>
              <CardDescription>
                Upload an image to enhance with AI upscaling
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-border rounded-lg p-12 text-center hover:border-primary/50 transition-colors">
                <Upload className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <p className="text-lg font-medium mb-2">Drop your image here</p>
                <p className="text-sm text-muted-foreground mb-4">
                  JPG, PNG, WEBP extensions allowed
                </p>
                <Button>Upload Image</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                Upscale Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Megapixels</label>
                <select className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm">
                  <option>2x (4 Megapixels)</option>
                  <option>4x (16 Megapixels)</option>
                  <option>8x (64 Megapixels)</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Strength</label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  defaultValue="5"
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Subtle</span>
                  <span>Strong</span>
                </div>
              </div>

              <Button className="w-full" size="lg">
                <Sparkles className="mr-2 h-5 w-5" />
                Start Upscale
              </Button>

              <div className="text-center text-sm text-muted-foreground">
                This will cost 3 gems
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Preview Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>
                Your upscaled image will appear here
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <ImageIcon className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Upload an image to see the preview
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Processing Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Status</span>
                  <span className="text-sm text-muted-foreground">Waiting for upload</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Processing Time</span>
                  <span className="text-sm text-muted-foreground">~30 seconds</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Quality</span>
                  <span className="text-sm text-green-600">Ultra High</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Features */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Why Choose Our Upscaler?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-12 w-12 mx-auto text-green-500 mb-4" />
              <h3 className="font-semibold mb-2">Superior Quality</h3>
              <p className="text-sm text-muted-foreground">
                Advanced AI algorithms produce the highest quality upscaled images
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Clock className="h-12 w-12 mx-auto text-blue-500 mb-4" />
              <h3 className="font-semibold mb-2">Lightning Fast</h3>
              <p className="text-sm text-muted-foreground">
                Process images in seconds, not minutes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Sparkles className="h-12 w-12 mx-auto text-purple-500 mb-4" />
              <h3 className="font-semibold mb-2">Skin Enhancement</h3>
              <p className="text-sm text-muted-foreground">
                Specialized in enhancing skin texture and details
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Before/After Examples */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Before & After Examples</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Portrait Enhancement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                    <span className="text-sm text-muted-foreground">Before</span>
                  </div>
                  <p className="text-xs text-center text-muted-foreground">Original 512x512</p>
                </div>
                <div className="space-y-2">
                  <div className="aspect-square bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-lg flex items-center justify-center">
                    <span className="text-sm text-green-700 dark:text-green-300">After</span>
                  </div>
                  <p className="text-xs text-center text-muted-foreground">Upscaled 2048x2048</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Detail Recovery</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                    <span className="text-sm text-muted-foreground">Before</span>
                  </div>
                  <p className="text-xs text-center text-muted-foreground">Low Resolution</p>
                </div>
                <div className="space-y-2">
                  <div className="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-lg flex items-center justify-center">
                    <span className="text-sm text-blue-700 dark:text-blue-300">After</span>
                  </div>
                  <p className="text-xs text-center text-muted-foreground">Enhanced Details</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Upscales */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Recent Upscales</h2>
        <Card>
          <CardContent className="p-12 text-center">
            <Zap className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-semibold mb-2">No Upscales Yet</h3>
            <p className="text-muted-foreground mb-6">
              Start by uploading your first image to enhance with AI upscaling.
            </p>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Your First Image
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
