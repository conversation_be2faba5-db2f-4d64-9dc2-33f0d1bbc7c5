"use client";

import React, { useEffect } from "react";
import { httpsCallable } from "firebase/functions";
import { functions } from "@/lib/firebase";

export default function Test() {
  useEffect(() => {
    const addMessage = httpsCallable(functions, "addMessage");
    const messageText = "World";
    addMessage({ text: messageText }).then((result) => {
      // Read result of the Cloud Function.
      /** @type {any} */
      const data = result.data;
      console.log(data);
    });
  }, []);
  return <div>page</div>;
}
