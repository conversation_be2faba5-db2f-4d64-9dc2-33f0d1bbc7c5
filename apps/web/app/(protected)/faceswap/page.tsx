import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@workspace/ui/components/card';
import { Button } from '@workspace/ui/components/button';
import {
  Upload,
  Users,
  Video,
  Image as ImageIcon,
  Sparkles,
  ArrowRight,
  Star,
  Zap,
  Shield,
  Clock,
} from 'lucide-react';

export default function FaceSwapPage() {
  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Users className="h-8 w-8 text-purple-500" />
          <h1 className="text-3xl font-bold">AI Face Swap</h1>
          <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-700 rounded-full">
            Popular
          </span>
        </div>
        <p className="text-muted-foreground text-lg">
          The most advanced photo and video face swap technology. Swap faces in
          seconds with realistic results.
        </p>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="group hover:shadow-lg transition-all duration-200">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-3 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                <ImageIcon className="h-6 w-6" />
              </div>
              <div>
                <CardTitle>Photo Face Swap</CardTitle>
                <CardDescription>
                  Swap faces in photos instantly
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Star className="h-4 w-4 text-yellow-500" />
                <span>High quality results</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Zap className="h-4 w-4 text-green-500" />
                <span>Process in seconds</span>
              </div>
              <Button className="w-full">
                Start Photo Swap
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-200">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-3 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                <Video className="h-6 w-6" />
              </div>
              <div>
                <CardTitle>Video Face Swap</CardTitle>
                <CardDescription>
                  Swap faces in videos seamlessly
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Star className="h-4 w-4 text-yellow-500" />
                <span>Premium feature</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Sparkles className="h-4 w-4 text-purple-500" />
                <span>Advanced AI technology</span>
              </div>
              <Button className="w-full" variant="outline">
                Start Video Swap
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="mr-2 h-5 w-5" />
            Upload Your Media
          </CardTitle>
          <CardDescription>
            Upload photos or videos to start face swapping
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
            <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-semibold mb-2">Drop your files here</h3>
            <p className="text-muted-foreground mb-4">
              Supports JPG, PNG, MP4, MOV files up to 100MB
            </p>
            <Button>
              Choose Files
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* How It Works */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">1. Upload Media</h3>
              <p className="text-sm text-muted-foreground">
                Upload your source photo/video and target face image
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold mb-2">2. AI Processing</h3>
              <p className="text-sm text-muted-foreground">
                Our advanced AI analyzes and swaps faces seamlessly
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <ArrowRight className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">3. Download Result</h3>
              <p className="text-sm text-muted-foreground">
                Get your face-swapped media in high quality
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Features */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3">
            <Shield className="h-5 w-5 text-green-500" />
            <span>Privacy protected - files deleted after processing</span>
          </div>
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-blue-500" />
            <span>Lightning fast processing</span>
          </div>
          <div className="flex items-center space-x-3">
            <Star className="h-5 w-5 text-yellow-500" />
            <span>Professional quality results</span>
          </div>
          <div className="flex items-center space-x-3">
            <Zap className="h-5 w-5 text-purple-500" />
            <span>Batch processing available</span>
          </div>
        </div>
      </div>

      {/* Recent Swaps */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Recent Face Swaps</h2>
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-semibold mb-2">No Face Swaps Yet</h3>
            <p className="text-muted-foreground mb-6">
              Start by uploading your first photo or video to begin face swapping.
            </p>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Your First Media
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
