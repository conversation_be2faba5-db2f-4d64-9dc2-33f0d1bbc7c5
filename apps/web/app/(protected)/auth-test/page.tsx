import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from '@workspace/ui/components/card';
import { getServerUser } from '@/lib/server-auth-utils';
import { AuthTestClient } from './AuthTestClient';

export default async function AuthTestPage() {
  // Get server-side user data
  const serverUser = await getServerUser();

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Auth Test Page</h1>
        <p className="text-muted-foreground">
          This page tests both server-side and client-side authentication.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Server-side Auth Info */}
        <Card>
          <CardHeader>
            <CardTitle>Server-side Auth (SSR)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {serverUser ? (
              <>
                <p><strong>Status:</strong> ✅ Authenticated</p>
                <p><strong>User ID:</strong> {serverUser.uid}</p>
                <p><strong>Email:</strong> {serverUser.email || 'N/A'}</p>
                <p><strong>Email Verified:</strong> {serverUser.emailVerified ? '✅' : '❌'}</p>
                <p className="text-sm text-green-600">
                  This data was fetched on the server during SSR.
                </p>
              </>
            ) : (
              <>
                <p><strong>Status:</strong> ❌ Not authenticated</p>
                <p className="text-sm text-red-600">
                  No server-side authentication detected.
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Client-side Auth Info */}
        <AuthTestClient />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Implementation Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">How SSR Auth Works:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Service worker intercepts requests and adds auth tokens to headers</li>
              <li>Middleware verifies tokens and adds user info to request headers</li>
              <li>Server components can access user data immediately</li>
              <li>Client components hydrate with server data to prevent flicker</li>
            </ol>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Benefits:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>No authentication flicker on page load</li>
              <li>Protected routes are truly protected (server-side)</li>
              <li>Better SEO for authenticated content</li>
              <li>Faster initial page loads with pre-rendered content</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
