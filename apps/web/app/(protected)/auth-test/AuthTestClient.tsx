'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { useHybridAuth } from '@/contexts/HybridAuthContext';

export function AuthTestClient() {
  const { user, loading, serverUser, isHydrated } = useHybridAuth();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Client-side Auth (CSR)</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <p><strong>Loading:</strong> {loading ? '⏳' : '✅'}</p>
        <p><strong>Hydrated:</strong> {isHydrated ? '✅' : '❌'}</p>
        
        {user ? (
          <>
            <p><strong>Status:</strong> ✅ Authenticated</p>
            <p><strong>User ID:</strong> {user.uid}</p>
            <p><strong>Email:</strong> {user.email || 'N/A'}</p>
            <p><strong>Email Verified:</strong> {user.emailVerified ? '✅' : '❌'}</p>
            <p className="text-sm text-blue-600">
              This data was fetched on the client after hydration.
            </p>
          </>
        ) : serverUser ? (
          <>
            <p><strong>Status:</strong> ⚡ Server data available</p>
            <p><strong>User ID:</strong> {serverUser.uid}</p>
            <p><strong>Email:</strong> {serverUser.email || 'N/A'}</p>
            <p><strong>Email Verified:</strong> {serverUser.emailVerified ? '✅' : '❌'}</p>
            <p className="text-sm text-orange-600">
              Using server data while client auth loads.
            </p>
          </>
        ) : (
          <>
            <p><strong>Status:</strong> ❌ Not authenticated</p>
            <p className="text-sm text-red-600">
              No client-side authentication detected.
            </p>
          </>
        )}

        <div className="mt-4 p-3 bg-muted rounded-lg">
          <h5 className="font-semibold text-sm mb-2">Auth State Details:</h5>
          <div className="text-xs space-y-1">
            <p>Client User: {user ? '✅' : '❌'}</p>
            <p>Server User: {serverUser ? '✅' : '❌'}</p>
            <p>Loading: {loading ? '✅' : '❌'}</p>
            <p>Hydrated: {isHydrated ? '✅' : '❌'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
