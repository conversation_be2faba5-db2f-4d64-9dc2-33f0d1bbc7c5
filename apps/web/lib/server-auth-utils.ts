import { headers } from 'next/headers';
import { redirect } from 'next/navigation';

export interface ServerUser {
  uid: string;
  email: string | null;
  emailVerified: boolean;
}

/**
 * Get user information from middleware headers
 * This should be called in Server Components
 */
export async function getServerUser(): Promise<ServerUser | null> {
  try {
    const headersList = await headers();
    const userId = headersList.get('x-user-id');
    const userEmail = headersList.get('x-user-email');
    const userVerified = headersList.get('x-user-verified');

    if (!userId) {
      return null;
    }

    return {
      uid: userId,
      email: userEmail || null,
      emailVerified: userVerified === 'true',
    };
  } catch (error) {
    console.error('Failed to get server user:', error);
    return null;
  }
}

/**
 * Require authentication in a Server Component
 * Redirects to sign-in if not authenticated
 */
export async function requireAuth(): Promise<ServerUser> {
  const user = await getServerUser();
  
  if (!user) {
    redirect('/auth/signin');
  }
  
  return user;
}

/**
 * Check if user is authenticated without redirecting
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getServerUser();
  return !!user;
}

/**
 * Get auth token from headers (for API calls)
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    const headersList = await headers();
    return headersList.get('x-firebase-auth-token');
  } catch (error) {
    console.error('Failed to get auth token:', error);
    return null;
  }
}

/**
 * Create authenticated fetch headers for server-side API calls
 */
export async function createAuthHeaders(): Promise<HeadersInit> {
  const authToken = await getAuthToken();
  const headers: HeadersInit = {};
  
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }
  
  return headers;
}
