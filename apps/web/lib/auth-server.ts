import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { verifyServerAuth, extractAuthToken, extractAppCheckToken } from './firebase-server';

export interface ServerAuthResult {
  isAuthenticated: boolean;
  user: {
    uid: string;
    email: string | null;
    displayName: string | null;
    photoURL: string | null;
    emailVerified: boolean;
  } | null;
  authToken?: string;
  appCheckToken?: string;
}

/**
 * Get authentication state on the server side
 * This function should be called in Server Components or Server Actions
 */
export async function getServerAuthState(): Promise<ServerAuthResult> {
  try {
    const headersList = await headers();
    const authToken = extractAuthToken(headersList);
    const appCheckToken = extractAppCheckToken(headersList);

    if (!authToken) {
      return {
        isAuthenticated: false,
        user: null,
        authToken,
        appCheckToken,
      };
    }

    const authResult = await verifyServerAuth(authToken);

    return {
      isAuthenticated: authResult.isAuthenticated,
      user: authResult.user,
      authToken,
      appCheckToken,
    };
  } catch (error) {
    console.error('Failed to get server auth state:', error);
    return {
      isAuthenticated: false,
      user: null,
    };
  }
}

/**
 * Require authentication on the server side
 * Redirects to sign-in page if not authenticated
 */
export async function requireServerAuth(): Promise<ServerAuthResult> {
  const authState = await getServerAuthState();
  
  if (!authState.isAuthenticated) {
    redirect('/auth/signin');
  }
  
  return authState;
}

/**
 * Get auth tokens from cookies (fallback method)
 * This is useful when service worker isn't available
 */
export async function getAuthTokensFromCookies(): Promise<{
  authToken?: string;
  appCheckToken?: string;
}> {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get('firebase-auth-token')?.value;
    const appCheckToken = cookieStore.get('firebase-appcheck-token')?.value;

    return { authToken, appCheckToken };
  } catch (error) {
    console.error('Failed to get auth tokens from cookies:', error);
    return {};
  }
}

/**
 * Set auth tokens in cookies
 * This should be called from a Server Action or API route
 */
export async function setAuthTokensInCookies(authToken?: string, appCheckToken?: string) {
  try {
    const cookieStore = await cookies();
    
    if (authToken) {
      cookieStore.set('firebase-auth-token', authToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
      });
    }
    
    if (appCheckToken) {
      cookieStore.set('firebase-appcheck-token', appCheckToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60, // 1 hour (app check tokens expire faster)
        path: '/',
      });
    }
  } catch (error) {
    console.error('Failed to set auth tokens in cookies:', error);
  }
}

/**
 * Clear auth tokens from cookies
 */
export async function clearAuthTokensFromCookies() {
  try {
    const cookieStore = await cookies();
    cookieStore.delete('firebase-auth-token');
    cookieStore.delete('firebase-appcheck-token');
  } catch (error) {
    console.error('Failed to clear auth tokens from cookies:', error);
  }
}

/**
 * Check if user has specific permissions (placeholder for future implementation)
 */
export async function checkUserPermissions(requiredPermissions: string[]): Promise<boolean> {
  const authState = await getServerAuthState();
  
  if (!authState.isAuthenticated) {
    return false;
  }

  // TODO: Implement actual permission checking logic
  // This could involve checking user roles in Firestore or custom claims
  return true;
}
