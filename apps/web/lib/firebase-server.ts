import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // For development with emulator, we don't need service account
  // For production, you would use a service account key
  admin.initializeApp({
    projectId: "charcaterise",
    // In production, add your service account:
    // credential: admin.credential.cert(serviceAccount),
  });

  // Connect to emulator in development
  if (process.env.NODE_ENV === "development") {
    process.env.FIREBASE_AUTH_EMULATOR_HOST = "127.0.0.1:9099";
  }
}

/**
 * Initialize Firebase Server App with auth and app check tokens
 */
export function initializeFirebaseServerApp(
  authIdToken?: string,
  appCheckToken?: string,
  releaseOnDeref?: object
) {
  const serverAppSettings: FirebaseServerAppSettings = {};

  if (authIdToken) {
    serverAppSettings.authIdToken = authIdToken;
  }

  if (appCheckToken) {
    serverAppSettings.appCheckToken = appCheckToken;
  }

  if (releaseOnDeref) {
    serverAppSettings.releaseOnDeref = releaseOnDeref;
  }

  const serverApp = initializeServerApp(firebaseConfig, serverAppSettings);
  return serverApp;
}

/**
 * Get server-side auth instance
 */
export function getServerAuth(
  authIdToken?: string,
  appCheckToken?: string,
  releaseOnDeref?: object
) {
  const serverApp = initializeFirebaseServerApp(
    authIdToken,
    appCheckToken,
    releaseOnDeref
  );
  return getAuth(serverApp);
}

/**
 * Get server-side Firestore instance
 */
export function getServerFirestore(
  authIdToken?: string,
  appCheckToken?: string,
  releaseOnDeref?: object
) {
  const serverApp = initializeFirebaseServerApp(
    authIdToken,
    appCheckToken,
    releaseOnDeref
  );
  return getFirestore(serverApp);
}

/**
 * Get server-side Functions instance
 */
export function getServerFunctions(
  authIdToken?: string,
  appCheckToken?: string,
  releaseOnDeref?: object
) {
  const serverApp = initializeFirebaseServerApp(
    authIdToken,
    appCheckToken,
    releaseOnDeref
  );
  return getFunctions(serverApp);
}

/**
 * Extract auth token from request headers
 */
export function extractAuthToken(headers: Headers): string | undefined {
  return headers.get("x-firebase-auth-token") || undefined;
}

/**
 * Extract app check token from request headers
 */
export function extractAppCheckToken(headers: Headers): string | undefined {
  return headers.get("x-firebase-appcheck-token") || undefined;
}

/**
 * Verify if user is authenticated on server-side
 */
export async function verifyServerAuth(authIdToken?: string): Promise<{
  isAuthenticated: boolean;
  user: any | null;
  error?: string;
}> {
  if (!authIdToken) {
    return { isAuthenticated: false, user: null };
  }

  try {
    const auth = getServerAuth(authIdToken);
    const user = auth.currentUser;

    return {
      isAuthenticated: !!user,
      user: user
        ? {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL,
            emailVerified: user.emailVerified,
          }
        : null,
    };
  } catch (error) {
    console.error("Server auth verification failed:", error);
    return {
      isAuthenticated: false,
      user: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
