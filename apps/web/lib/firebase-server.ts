import { initializeServerApp, FirebaseServerAppSettings } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';

// Firebase configuration (same as client-side)
const firebaseConfig = {
  apiKey: "AIzaSyAGqdHqCMd9wRjHVSEHA6f1KIuVQtnuVZc",
  authDomain: "charcaterise.firebaseapp.com",
  projectId: "charcaterise",
  storageBucket: "charcaterise.firebasestorage.app",
  messagingSenderId: "581872908064",
  appId: "1:581872908064:web:fdfbadf078bd3285344945",
  measurementId: "G-324WPYYYR8",
};

/**
 * Initialize Firebase Server App with auth and app check tokens
 */
export function initializeFirebaseServerApp(
  authIdToken?: string,
  appCheckToken?: string,
  releaseOnDeref?: object
) {
  const serverAppSettings: FirebaseServerAppSettings = {};
  
  if (authIdToken) {
    serverAppSettings.authIdToken = authIdToken;
  }
  
  if (appCheckToken) {
    serverAppSettings.appCheckToken = appCheckToken;
  }
  
  if (releaseOnDeref) {
    serverAppSettings.releaseOnDeref = releaseOnDeref;
  }

  const serverApp = initializeServerApp(firebaseConfig, serverAppSettings);
  return serverApp;
}

/**
 * Get server-side auth instance
 */
export function getServerAuth(authIdToken?: string, appCheckToken?: string, releaseOnDeref?: object) {
  const serverApp = initializeFirebaseServerApp(authIdToken, appCheckToken, releaseOnDeref);
  return getAuth(serverApp);
}

/**
 * Get server-side Firestore instance
 */
export function getServerFirestore(authIdToken?: string, appCheckToken?: string, releaseOnDeref?: object) {
  const serverApp = initializeFirebaseServerApp(authIdToken, appCheckToken, releaseOnDeref);
  return getFirestore(serverApp);
}

/**
 * Get server-side Functions instance
 */
export function getServerFunctions(authIdToken?: string, appCheckToken?: string, releaseOnDeref?: object) {
  const serverApp = initializeFirebaseServerApp(authIdToken, appCheckToken, releaseOnDeref);
  return getFunctions(serverApp);
}

/**
 * Extract auth token from request headers
 */
export function extractAuthToken(headers: Headers): string | undefined {
  return headers.get('x-firebase-auth-token') || undefined;
}

/**
 * Extract app check token from request headers
 */
export function extractAppCheckToken(headers: Headers): string | undefined {
  return headers.get('x-firebase-appcheck-token') || undefined;
}

/**
 * Verify if user is authenticated on server-side
 */
export async function verifyServerAuth(authIdToken?: string): Promise<{
  isAuthenticated: boolean;
  user: any | null;
  error?: string;
}> {
  if (!authIdToken) {
    return { isAuthenticated: false, user: null };
  }

  try {
    const auth = getServerAuth(authIdToken);
    const user = auth.currentUser;
    
    return {
      isAuthenticated: !!user,
      user: user ? {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
      } : null,
    };
  } catch (error) {
    console.error('Server auth verification failed:', error);
    return {
      isAuthenticated: false,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
