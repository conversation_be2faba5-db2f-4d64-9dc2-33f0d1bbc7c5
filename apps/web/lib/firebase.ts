// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getAnalytics } from "firebase/analytics";
import { connectFunctionsEmulator, getFunctions } from "firebase/functions";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAGqdHqCMd9wRjHVSEHA6f1KIuVQtnuVZc",
  authDomain: "charcaterise.firebaseapp.com",
  projectId: "charcaterise",
  storageBucket: "charcaterise.firebasestorage.app",
  messagingSenderId: "581872908064",
  appId: "1:581872908064:web:fdfbadf078bd3285344945",
  measurementId: "G-324WPYYYR8",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

connectAuthEmulator(auth, "http://127.0.0.1:9099");

export const functions = getFunctions(app);
connectFunctionsEmulator(functions, "localhost", 5001);

// Initialize Analytics (only in browser environment)
export const analytics =
  typeof window !== "undefined" ? getAnalytics(app) : null;

export default app;
