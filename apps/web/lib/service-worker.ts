'use client';

/**
 * Service Worker registration and management for Firebase Auth SSR
 */

let serviceWorkerRegistration: ServiceWorkerRegistration | null = null;

/**
 * Register the Firebase Auth service worker
 */
export async function registerAuthServiceWorker(): Promise<boolean> {
  // Only register in browser environment
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.warn('Service Worker not supported');
    return false;
  }

  try {
    // Check if already registered
    if (serviceWorkerRegistration) {
      console.log('Service Worker already registered');
      return true;
    }

    // Register the service worker
    serviceWorkerRegistration = await navigator.serviceWorker.register(
      '/firebase-auth-sw.js',
      {
        scope: '/',
      }
    );

    console.log('Firebase Auth Service Worker registered successfully');

    // Handle service worker updates
    serviceWorkerRegistration.addEventListener('updatefound', () => {
      const newWorker = serviceWorkerRegistration?.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            console.log('New Firebase Auth Service Worker available');
            // Optionally notify user about update
          }
        });
      }
    });

    return true;
  } catch (error) {
    console.error('Failed to register Firebase Auth Service Worker:', error);
    return false;
  }
}

/**
 * Unregister the service worker
 */
export async function unregisterAuthServiceWorker(): Promise<boolean> {
  if (!serviceWorkerRegistration) {
    return true;
  }

  try {
    const result = await serviceWorkerRegistration.unregister();
    serviceWorkerRegistration = null;
    console.log('Firebase Auth Service Worker unregistered');
    return result;
  } catch (error) {
    console.error('Failed to unregister Firebase Auth Service Worker:', error);
    return false;
  }
}

/**
 * Check if service worker is active
 */
export function isServiceWorkerActive(): boolean {
  return !!(
    serviceWorkerRegistration &&
    serviceWorkerRegistration.active &&
    serviceWorkerRegistration.active.state === 'activated'
  );
}

/**
 * Wait for service worker to be ready
 */
export async function waitForServiceWorker(timeout = 10000): Promise<boolean> {
  if (!('serviceWorker' in navigator)) {
    return false;
  }

  return new Promise((resolve) => {
    const timeoutId = setTimeout(() => resolve(false), timeout);

    const checkServiceWorker = () => {
      if (isServiceWorkerActive()) {
        clearTimeout(timeoutId);
        resolve(true);
      } else {
        setTimeout(checkServiceWorker, 100);
      }
    };

    checkServiceWorker();
  });
}

/**
 * Force service worker to update tokens
 * This can be called after auth state changes
 */
export async function refreshServiceWorkerTokens(): Promise<void> {
  if (!serviceWorkerRegistration || !serviceWorkerRegistration.active) {
    return;
  }

  try {
    // Send message to service worker to refresh tokens
    serviceWorkerRegistration.active.postMessage({
      type: 'REFRESH_TOKENS',
    });
  } catch (error) {
    console.error('Failed to refresh service worker tokens:', error);
  }
}

/**
 * Initialize service worker with proper error handling
 */
export async function initializeServiceWorker(): Promise<void> {
  try {
    const registered = await registerAuthServiceWorker();
    if (registered) {
      await waitForServiceWorker();
      console.log('Firebase Auth Service Worker initialized successfully');
    }
  } catch (error) {
    console.error('Failed to initialize Firebase Auth Service Worker:', error);
  }
}
