import { getFunctions, httpsCallable } from "firebase/functions";
import app from "./firebase";

// Initialize Firebase Functions
const functions = getFunctions(app);

// Type definitions
export interface GenerateImageRequest {
  prompt: string;
  model?: string;
  image_size?: string;
  num_inference_steps?: number;
  guidance_scale?: number;
  seed?: number;
  enable_safety_checker?: boolean;
}

export interface GenerateImageResponse {
  success: boolean;
  requestId: string;
  message: string;
}

export interface JobStatusRequest {
  requestId: string;
}

export interface JobStatusResponse {
  requestId: string;
  status: "SUBMITTED" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  userId: string;
  type: string;
  mediaType: string;
  originalUrl?: string;
  firebaseUrl?: string;
  error?: string;
  createdAt: any;
  updatedAt: any;
}

export interface UserMediaResponse {
  media: Array<{
    id: string;
    requestId: string;
    type: string;
    mediaType: string;
    firebaseUrl: string;
    originalUrl: string;
    createdAt: any;
  }>;
}

// Cloud Functions
export const generateImage = httpsCallable<
  GenerateImageRequest,
  GenerateImageResponse
>(functions, "generateImage");

export const getJobStatus = httpsCallable<JobStatusRequest, JobStatusResponse>(
  functions,
  "getJobStatusAuth"
);

export const getUserMedia = httpsCallable<
  { limit?: number },
  UserMediaResponse
>(functions, "getUserMediaAuth");

// Helper function to poll job status until completion
export async function pollJobStatus(
  requestId: string,
  onUpdate?: (status: JobStatusResponse) => void,
  maxAttempts = 30,
  intervalMs = 2000
): Promise<JobStatusResponse> {
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const result = await getJobStatus({ requestId });
      const status = result.data;

      if (onUpdate) {
        onUpdate(status);
      }

      if (status.status === "COMPLETED" || status.status === "FAILED") {
        return status;
      }

      // Wait before next poll
      await new Promise((resolve) => setTimeout(resolve, intervalMs));
      attempts++;
    } catch (error) {
      console.error("Error polling job status:", error);
      attempts++;

      if (attempts >= maxAttempts) {
        throw error;
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, intervalMs));
    }
  }

  throw new Error("Max polling attempts reached");
}
