{"name": "@workspace/types", "version": "0.0.0", "type": "module", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "typecheck": "tsc --noEmit"}, "devDependencies": {"@types/react": "^19.1.9", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "typescript": "^5.9.2"}, "exports": {".": "./src/index.ts", "./auth": "./src/auth.ts", "./ui": "./src/ui.ts", "./api": "./src/api.ts"}}