// API related types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// AI Generation API types
export interface ImageGenerationRequest {
  prompt: string;
  style?: string;
  size?: string;
  quality?: string;
}

export interface ImageGenerationResponse {
  imageUrl: string;
  prompt: string;
  generationId: string;
}

export interface FaceSwapRequest {
  sourceImageUrl: string;
  targetImageUrl: string;
}

export interface FaceSwapResponse {
  resultImageUrl: string;
  processingId: string;
}
