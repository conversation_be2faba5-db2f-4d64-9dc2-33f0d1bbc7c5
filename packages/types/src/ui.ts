import type { ReactNode } from "react";

// Layout component types
export interface AppLayoutProps {
  children: ReactNode;
  className?: string;
}

export interface SidebarItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
}

export interface SidebarSection {
  title: string;
  items: SidebarItem[];
}

export interface HeaderProps {
  onMenuClick?: () => void;
  className?: string;
}

// Common component props
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

// Image generation types
export interface ImageGeneratorProps {
  onImageGenerated?: (imageUrl: string) => void;
}

export interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  createdAt: Date;
}
