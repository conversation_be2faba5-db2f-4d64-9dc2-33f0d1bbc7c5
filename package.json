{"name": "shadcn-ui-monorepo", "version": "0.0.1", "private": true, "scripts": {"firebase:serve": "pnpm run build --filter=api && firebase emulators:start ", "build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}}